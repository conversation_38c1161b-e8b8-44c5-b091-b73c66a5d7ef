// Subtitle Analysis Module - 字幕分析功能模块
class SubtitleAnalysis {
    constructor(core) {
        this.core = core;
        
        // 保存实例到全局变量，用于模态框关闭时的清理
        window.subtitleAnalysis = this;
        
        console.log('字幕分析管理器初始化完成');
    }

    // AI解析当前字幕
    async analyzeCurrentSubtitle() {
        const currentJa = this.core.lastSubtitleText.ja;
        const currentZh = this.core.lastSubtitleText.zh;
        
        if (!currentJa || currentJa.trim() === '') {
            if (this.core.ui) {
                this.core.ui.showNotification('当前无日文字幕可分析', 'warning');
            }
            return;
        }

        // 使用新的分析面板替代模态框
        this.showAnalysisPanel(currentJa, currentZh || '');
    }

    // 显示分析面板
    async showAnalysisPanel(jaText, zhText) {
        // 检查是否已加载分析面板JS
        if (typeof window.analyzeSubtitle !== 'function') {
            console.log('加载分析面板JS');
            // 加载分析面板样式
            const panelCss = document.createElement('link');
            panelCss.rel = 'stylesheet';
            panelCss.href = chrome.runtime.getURL('components/analysis-panel/analysis-panel.css');
            document.head.appendChild(panelCss);

            // 加载分析面板JS
            const panelScript = document.createElement('script');
            panelScript.src = chrome.runtime.getURL('components/analysis-panel/analysis-panel.js');
            document.head.appendChild(panelScript);
            
            // 等待JS加载完成
            await new Promise(resolve => {
                panelScript.onload = resolve;
                // 5秒超时
                setTimeout(resolve, 5000);
            });
        }
        
        // 初始化分析面板
        if (typeof window.initAnalysisPanel === 'function') {
            window.initAnalysisPanel();
        } else {
            console.error('分析面板初始化函数未找到');
            if (this.core.ui) {
                this.core.ui.showNotification('分析面板加载失败', 'error');
            }
            return;
        }
        
        // 分析字幕
        if (typeof window.analyzeSubtitle === 'function') {
            window.analyzeSubtitle(jaText, zhText);
        } else {
            console.error('分析字幕函数未找到');
            if (this.core.ui) {
                this.core.ui.showNotification('分析功能加载失败', 'error');
            }
        }
    }

    // 生成视频摘要
    generateVideoSummary(retryCount = 0) {
        // 获取当前视频URL
        const videoUrl = window.location.href;
        if (!videoUrl.includes('youtube.com/watch')) {
            if (this.core.ui) {
                this.core.ui.showNotification('请在YouTube视频页面使用此功能', 'error');
            }
            return;
        }
        
        // 显示摘要模态框
        const summaryModal = document.getElementById('extension-summary-modal');
        if (!summaryModal) {
            console.error('摘要模态框未找到');
            return;
        }
        
        const loadingEl = summaryModal.querySelector('.summary-loading');
        const resultEl = summaryModal.querySelector('.summary-result');
        const contentEl = summaryModal.querySelector('.summary-content');
        const titleEl = summaryModal.querySelector('.summary-video-title');
        
        summaryModal.style.display = 'block';
        if (loadingEl) loadingEl.style.display = 'flex';
        if (resultEl) resultEl.style.display = 'none';
        if (contentEl) contentEl.textContent = '';
        if (titleEl) titleEl.textContent = '';
        
        // 检查是否有字幕数据（优先检查前端缓存的字幕数据）
        let hasSubtitleData = false;

        // 首先检查是否有缓存的字幕数据
        try {
            if (window.SubtitleListener) {
                console.log('SubtitleListener 可用，检查缓存数据...');
                const cachedData = window.SubtitleListener.getAllCachedSubtitleData();
                console.log('缓存数据:', cachedData);
                if (cachedData && Object.keys(cachedData).length > 0) {
                    hasSubtitleData = true;
                    console.log('找到缓存的字幕数据，URL数量:', Object.keys(cachedData).length);
                } else {
                    console.log('缓存数据为空或不存在');
                }
            } else {
                console.log('SubtitleListener 不可用');
            }
        } catch (error) {
            console.error('检查字幕数据失败:', error);
        }

        // 如果没有缓存的字幕数据，尝试主动获取（最多重试1次）
        if (!hasSubtitleData && retryCount === 0) {
            console.log('没有找到缓存的字幕数据，尝试主动触发字幕获取...');

            // 尝试触发字幕监听器
            try {
                if (window.SubtitleListener) {
                    // 点击字幕按钮触发字幕加载
                    window.SubtitleListener.clickSubtitleButton();

                    // 等待一段时间后重新检查
                    if (loadingEl) {
                        const loadingText = loadingEl.querySelector('.loading-text');
                        const loadingSubtext = loadingEl.querySelector('.loading-subtext');
                        if (loadingText) loadingText.textContent = '正在获取字幕数据...';
                        if (loadingSubtext) loadingSubtext.textContent = '请稍候，正在从YouTube获取字幕';
                    }

                    // 延迟3秒后重新尝试（只重试一次）
                    setTimeout(() => {
                        this.generateVideoSummary(1);
                    }, 3000);
                    return;
                }
            } catch (error) {
                console.error('触发字幕获取失败:', error);
            }
        }

        // 如果还是没有数据且没有文件ID，显示错误
        if (!hasSubtitleData && !this.core.subtitleFileId) {
            if (loadingEl) {
                const loadingText = loadingEl.querySelector('.loading-text');
                const loadingSubtext = loadingEl.querySelector('.loading-subtext');
                if (loadingText) loadingText.textContent = '无法生成摘要，请先加载字幕';
                if (loadingSubtext) loadingSubtext.textContent = '请确保视频有可用的字幕';
            }
            setTimeout(() => {
                summaryModal.style.display = 'none';
            }, 2000);
            return;
        }
        
        // 尝试获取视频标题
        let videoTitle = document.title.replace(' - YouTube', '');
        
        // 去除标题前面的序号前缀，如"(2) "、"[2] "、"#1 "等
        videoTitle = videoTitle.replace(/^\s*(?:\(\d+\)|\[\d+\]|#\d+)\s+/i, '');

        // 获取缓存的字幕数据
        let subtitleContent = null;
        try {
            if (window.SubtitleListener) {
                console.log('开始获取字幕内容...');
                const cachedData = window.SubtitleListener.getAllCachedSubtitleData();
                console.log('获取到的缓存数据:', cachedData);

                if (cachedData && Object.keys(cachedData).length > 0) {
                    // 优先查找中文字幕URL
                    let selectedUrl = null;
                    const urls = Object.keys(cachedData);
                    console.log('可用的字幕URL:', urls);

                    for (const url of urls) {
                        if (url.includes('tlang=zh') || url.includes('lang=zh')) {
                            selectedUrl = url;
                            console.log('找到中文字幕URL:', url);
                            break;
                        }
                    }

                    // 如果没有找到中文字幕，使用第一个可用的字幕
                    if (!selectedUrl) {
                        selectedUrl = urls[0];
                        console.log('未找到中文字幕，使用第一个可用字幕:', selectedUrl);
                    }

                    const selectedData = cachedData[selectedUrl];
                    console.log('选中的字幕数据:', selectedData);

                    subtitleContent = JSON.stringify(selectedData);
                    console.log('获取到缓存的字幕数据，大小:', subtitleContent.length);
                } else {
                    console.warn('未找到缓存的字幕数据，cachedData:', cachedData);
                }
            } else {
                console.warn('SubtitleListener未找到');
            }
        } catch (error) {
            console.error('获取字幕数据失败:', error);
        }

        // 通过background.js向后端请求生成摘要
        chrome.runtime.sendMessage({
            action: 'generateVideoSummary',
            subtitleFileId: this.core.subtitleFileId || 'frontend-cache', // 如果没有文件ID，使用占位符
            videoUrl: videoUrl,
            subtitleContent: subtitleContent // 传递字幕内容
        }, (response) => {
            if (chrome.runtime.lastError) {
                this.showSummaryError(loadingEl, '生成摘要失败', chrome.runtime.lastError.message);
                return;
            }
            
            if (!response || !response.success) {
                // 检查是否为Premium权限错误
                if (response && response.data && response.data.error === 'PREMIUM_REQUIRED') {
                    this.showSummaryError(loadingEl, 'Premium权限不足', '视频摘要功能仅限Premium会员使用，请升级您的会员等级');
                } else if (response && response.data && response.data.error === 'AUTHENTICATION_REQUIRED') {
                    this.showSummaryError(loadingEl, '认证失败', '用户认证已过期，请重新登录');
                } else {
                    this.showSummaryError(loadingEl, '生成摘要失败', response?.error || '未知错误');
                }
                return;
            }
            
            // 显示摘要内容
            if (loadingEl) loadingEl.style.display = 'none';
            if (resultEl) resultEl.style.display = 'block';
            
            // 设置视频标题
            if (titleEl) {
                titleEl.textContent = videoTitle || response.videoTitle || '视频摘要';
            }
            
            // 设置摘要内容
            if (contentEl) {
                contentEl.textContent = response.summary || '摘要生成成功，但内容为空';
            }
        });
    }
    
    // 生成思维导图
    generateMindMap() {
        // 获取当前视频URL
        const videoUrl = window.location.href;
        if (!videoUrl.includes('youtube.com/watch')) {
            if (this.core.ui) {
                this.core.ui.showNotification('请在YouTube视频页面使用此功能', 'error');
            }
            return;
        }
        
        // 检查是否有字幕文件ID
        if (!this.core.subtitleFileId) {
            if (this.core.ui) {
                this.core.ui.showNotification('无法生成思维导图，请先加载字幕', 'error');
            }
            return;
        }
        
        // 显示思维导图模态框
        this.showMindMapModal();
        
        // 尝试获取视频标题
        let videoTitle = document.title.replace(' - YouTube', '');
        
        // 去除标题前面的序号前缀，如"(2) "、"[2] "、"#1 "等
        videoTitle = videoTitle.replace(/^\s*(?:\(\d+\)|\[\d+\]|#\d+)\s+/i, '');
        
        // 通过background.js向后端请求生成思维导图
        chrome.runtime.sendMessage({
            action: 'generateMindMap',
            subtitleFileId: this.core.subtitleFileId,
            videoUrl: videoUrl
        }, (response) => {
            if (chrome.runtime.lastError) {
                this.showMindMapError('生成思维导图失败', chrome.runtime.lastError.message);
                return;
            }
            
            // 后端直接返回思维导图数据或错误对象
            if (response && response.error) {
                // 检查是否为Plus/Premium权限错误
                if (response.error === 'PLUS_OR_PREMIUM_REQUIRED') {
                    this.showMindMapError('Plus/Premium权限不足', '思维导图功能仅限Plus或Premium会员使用，请升级您的会员等级');
                } else if (response.error === 'AUTHENTICATION_REQUIRED') {
                    this.showMindMapError('认证失败', '用户认证已过期，请重新登录');
                } else {
                    this.showMindMapError('生成思维导图失败', response.message || '未知错误');
                }
                return;
            }
            
            // 成功的响应现在直接是mindMap数据
            if (response && response.id === 'root') {
            // 显示思维导图内容
                this.showMindMapContent(response, videoTitle || '思维导图');
            } else {
                this.showMindMapError('生成思维导图失败', '返回的数据格式不正确');
            }
        });
    }
    
    // 显示思维导图模态框
    showMindMapModal() {
        // 创建思维导图模态框
        let mindMapModal = document.getElementById('extension-mindmap-modal');
        if (!mindMapModal) {
            mindMapModal = document.createElement('div');
            mindMapModal.id = 'extension-mindmap-modal';
            mindMapModal.className = 'extension-mindmap-modal';
            mindMapModal.innerHTML = `
                <div class="mindmap-modal-content">
                    <div class="mindmap-header">
                        <h3 class="mindmap-title">思维导图</h3>
                        <button class="mindmap-close-btn" onclick="
                        const modal = this.closest('.extension-mindmap-modal');
                        modal.style.display='none';
                        // 调用清理函数
                        if (window.subtitleAnalysis && window.subtitleAnalysis.mindMapCleanup) {
                            window.subtitleAnalysis.mindMapCleanup();
                        }
                    ">×</button>
                    </div>
                    <div class="mindmap-loading" style="display: flex;">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">正在生成思维导图...</div>
                        <div class="loading-subtext">请稍候，这可能需要几秒钟</div>
                    </div>
                    <div class="mindmap-result" style="display: none;">
                        <div class="mindmap-video-title"></div>
                        <div class="mindmap-container">
                            <div class="mindmap-controls">
                                <button class="mindmap-control-btn" id="mindmap-zoom-in" title="放大">+</button>
                                <button class="mindmap-control-btn" id="mindmap-zoom-out" title="缩小">-</button>
                                <button class="mindmap-control-btn" id="mindmap-reset" title="重置">⌂</button>
                            </div>
                            <div class="mindmap-canvas" id="mindmap-canvas"></div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(mindMapModal);
            
            // 添加样式
            if (!document.getElementById('mindmap-modal-styles')) {
                const style = document.createElement('style');
                style.id = 'mindmap-modal-styles';
                style.textContent = `
                    .extension-mindmap-modal {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.8);
                        display: none;
                        z-index: 10003;
                        backdrop-filter: blur(5px);
                    }
                    
                    .mindmap-modal-content {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background: white;
                        border-radius: 12px;
                        width: 90%;
                        max-width: 1200px;
                        height: 80%;
                        max-height: 800px;
                        display: flex;
                        flex-direction: column;
                        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    }
                    
                    .mindmap-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 20px;
                        border-bottom: 1px solid #eee;
                    }
                    
                    .mindmap-title {
                        margin: 0;
                        font-size: 20px;
                        font-weight: 600;
                        color: #333;
                    }
                    
                    .mindmap-close-btn {
                        background: none;
                        border: none;
                        font-size: 24px;
                        cursor: pointer;
                        color: #666;
                        padding: 5px;
                        line-height: 1;
                    }
                    
                    .mindmap-close-btn:hover {
                        color: #333;
                    }
                    
                    .mindmap-loading {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        padding: 40px;
                    }
                    
                    .loading-spinner {
                        width: 40px;
                        height: 40px;
                        border: 3px solid #f3f3f3;
                        border-top: 3px solid #4A90E2;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        margin-bottom: 20px;
                    }
                    
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                    
                    .loading-text {
                        font-size: 16px;
                        font-weight: 500;
                        color: #333;
                        margin-bottom: 8px;
                    }
                    
                    .loading-subtext {
                        font-size: 14px;
                        color: #666;
                    }
                    
                    .mindmap-result {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        padding: 20px;
                    }
                    
                    .mindmap-video-title {
                        font-size: 18px;
                        font-weight: 600;
                        color: #333;
                        margin-bottom: 20px;
                        text-align: center;
                    }
                    
                    .mindmap-container {
                        flex: 1;
                        position: relative;
                        background: #f9f9f9;
                        border-radius: 8px;
                        overflow: hidden;
                    }
                    
                    .mindmap-controls {
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        display: flex;
                        gap: 5px;
                        z-index: 10;
                    }
                    
                    .mindmap-control-btn {
                        width: 32px;
                        height: 32px;
                        border: none;
                        background: white;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 16px;
                        font-weight: bold;
                        color: #333;
                        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                        transition: all 0.2s ease;
                    }
                    
                    .mindmap-control-btn:hover {
                        background: #f0f0f0;
                        transform: translateY(-1px);
                    }
                    
                    .mindmap-canvas {
                        width: 100%;
                        height: 100%;
                        overflow: hidden;
                        position: relative;
                    }
                    
                    /* 黑暗模式样式 */
                    body.dark-mode .mindmap-modal-content,
                    html.dark-mode .mindmap-modal-content {
                        background: #1a202c;
                        color: #e2e8f0;
                    }
                    
                    body.dark-mode .mindmap-header,
                    html.dark-mode .mindmap-header {
                        border-bottom: 1px solid #4a5568;
                    }
                    
                    body.dark-mode .mindmap-title,
                    html.dark-mode .mindmap-title {
                        color: #e2e8f0;
                    }
                    
                    body.dark-mode .mindmap-close-btn,
                    html.dark-mode .mindmap-close-btn {
                        color: #9ca3af;
                    }
                    
                    body.dark-mode .mindmap-close-btn:hover,
                    html.dark-mode .mindmap-close-btn:hover {
                        color: #e2e8f0;
                    }
                    
                    body.dark-mode .loading-spinner,
                    html.dark-mode .loading-spinner {
                        border: 3px solid #4a5568;
                        border-top: 3px solid #4299e1;
                    }
                    
                    body.dark-mode .loading-text,
                    html.dark-mode .loading-text {
                        color: #e2e8f0;
                    }
                    
                    body.dark-mode .loading-subtext,
                    html.dark-mode .loading-subtext {
                        color: #9ca3af;
                    }
                    
                    body.dark-mode .mindmap-video-title,
                    html.dark-mode .mindmap-video-title {
                        color: #e2e8f0;
                    }
                    
                    body.dark-mode .mindmap-container,
                    html.dark-mode .mindmap-container {
                        background: #1a202c;
                    }
                `;
                document.head.appendChild(style);
            }
        }
        
        mindMapModal.style.display = 'block';
    }
    
    // 显示思维导图错误信息
    showMindMapError(title, message) {
        const mindMapModal = document.getElementById('extension-mindmap-modal');
        if (!mindMapModal) return;
        
        const loadingEl = mindMapModal.querySelector('.mindmap-loading');
        const resultEl = mindMapModal.querySelector('.mindmap-result');
        
        if (loadingEl) {
            loadingEl.innerHTML = `
                <div style="color: #e74c3c; font-size: 48px; margin-bottom: 20px;">⚠</div>
                <div class="loading-text" style="color: #e74c3c;">${title}</div>
                <div class="loading-subtext">${message}</div>
            `;
        }
        
        if (resultEl) resultEl.style.display = 'none';
        
        // 3秒后自动关闭
        setTimeout(() => {
            mindMapModal.style.display = 'none';
        }, 3000);
    }
    
    // 显示思维导图内容
    showMindMapContent(mindMapData, videoTitle) {
        const mindMapModal = document.getElementById('extension-mindmap-modal');
        if (!mindMapModal) return;
        
        const loadingEl = mindMapModal.querySelector('.mindmap-loading');
        const resultEl = mindMapModal.querySelector('.mindmap-result');
        const titleEl = mindMapModal.querySelector('.mindmap-video-title');
        const canvasEl = mindMapModal.querySelector('.mindmap-canvas');
        
        if (loadingEl) loadingEl.style.display = 'none';
        if (resultEl) resultEl.style.display = 'flex';
        if (titleEl) titleEl.textContent = videoTitle;
        
        // 渲染思维导图
        if (canvasEl) {
            // ECharts已通过manifest.json加载，直接使用
            if (typeof echarts !== 'undefined') {
                this.renderMindMapWithECharts(canvasEl, mindMapData, videoTitle);
            } else {
                console.error('ECharts库未正确加载');
                this.showMindMapError('渲染失败', 'ECharts库未正确加载');
            }
        }
    }

    // 使用ECharts渲染思维导图
    renderMindMapWithECharts(container, mindMapData, videoTitle) {
        // 1. 初始化ECharts实例
        const myChart = echarts.init(container);

        // 2. 检测当前主题
        const isDarkMode = this.isDarkMode();

        // 3. 根据主题选择颜色方案
        const colorScheme = this.getColorScheme(isDarkMode);

        // 4. 将我们的数据转换为ECharts需要的数据格式
        const convertData = (node, parentLevel = -1, colorIndex = 0) => {
            if (!node) return null;
            
            const result = {
                name: node.text,
                value: node.id,
                level: node.level,
                hasChildren: node.children && node.children.length > 0,
                children: []
            };

            // 为第一层子节点分配颜色
            if (node.level === 1) {
                result.branchColor = colorScheme.branchColors[colorIndex % colorScheme.branchColors.length].name;
            }

            // 递归处理子节点
            if (node.children && node.children.length > 0) {
                result.children = node.children.map((child, index) => {
                    // 如果当前是根节点，为每个子节点分配不同的颜色
                    const childColorIndex = node.level === 0 ? index : colorIndex;
                    return convertData(child, node.level, childColorIndex);
                });
            }

            return result;
        };
        
        const echartsData = convertData(mindMapData);

        // 5. 配置ECharts选项
        const option = {
            backgroundColor: colorScheme.backgroundColor,
            tooltip: {
                trigger: 'item',
                triggerOn: 'mousemove',
                formatter: '{b}',
                backgroundColor: colorScheme.tooltipBg,
                borderColor: colorScheme.tooltipBorder,
                textStyle: {
                    color: colorScheme.tooltipText
                }
            },
            series: [
                {
                    type: 'tree',
                    data: [echartsData],
                    top: '5%',
                    bottom: '5%',
                    left: '10%',
                    right: '25%',
                    symbol: 'none',
                    symbolSize: 0,
                    orient: 'LR', // 从左到右
                    expandAndCollapse: true,
                    animationDuration: 550,
                    animationDurationUpdate: 750,
                    // 启用拖拽和缩放
                    roam: true,
                    // 初始缩放级别
                    zoom: 1,
                    // 缩放范围
                    scaleLimit: {
                        min: 0.5,
                        max: 3
                    },

                    label: {
                        position: 'right',
                        verticalAlign: 'middle',
                        align: 'left',
                        backgroundColor: colorScheme.labelBg,
                        padding: [5, 10],
                        borderRadius: 5,
                        borderWidth: 1,
                        borderColor: colorScheme.labelBorder,
                        formatter: '{b}',
                        rich: {},

                        // 根据层级设置不同样式
                        normal: {
                            formatter: function (params) {
                                const data = params.data;
                                let icon = '';
                                
                                // 为有子节点的节点添加展开/折叠图标
                                if (data.hasChildren) {
                                    // 检查节点是否折叠（ECharts会在运行时设置这个属性）
                                    const isCollapsed = params.collapsed === true;
                                    icon = isCollapsed ? '▶ ' : '▼ ';
                                }
                                
                                // 根据层级设置不同样式
                                if (data.level === 0) {
                                    return `{root|${icon}${params.name}}`;
                                }
                                if (data.level === 1) {
                                    // 使用动态颜色
                                    const colorKey = `branch_${data.branchColor}`;
                                    return `{${colorKey}|${icon}${params.name}}`;
                                }
                                return `{leaf|${icon}${params.name}}`;
                            },
                            rich: {
                                root: {
                                    fontSize: 16,
                                    fontWeight: 'bold',
                                    color: colorScheme.rootText,
                                    backgroundColor: colorScheme.rootBg,
                                    borderColor: colorScheme.rootBg,
                                    padding: [8, 15],
                                },
                                leaf: {
                                    fontSize: 13,
                                    color: colorScheme.leafText,
                                    backgroundColor: colorScheme.leafBg,
                                    borderColor: colorScheme.leafBorder,
                                    padding: [5, 10],
                                }
                            }
                        }
                    },

                    leaves: {
                        label: {
                            position: 'right',
                            verticalAlign: 'middle',
                            align: 'left',
                        }
                    },

                    lineStyle: {
                        color: colorScheme.lineColor,
                        width: 1.5,
                        curveness: 0.5 // 设置线的曲度
                    }
                }
            ]
        };

        // 6. 动态添加分支颜色样式到rich配置中
        colorScheme.branchColors.forEach(colorObj => {
            const colorKey = `branch_${colorObj.name}`;
            option.series[0].label.normal.rich[colorKey] = {
                fontSize: 14,
                fontWeight: 'bold',
                color: colorScheme.branchText,
                backgroundColor: colorObj.color,
                borderColor: colorObj.color,
                padding: [6, 12],
            };
        });

        // 7. 应用配置并渲染
        myChart.setOption(option);

        // 8. 添加自定义缩放控制按钮
        this.addZoomControls(container, myChart, colorScheme);

        // 9. 监听窗口大小变化，自适应重绘
        const resizeHandler = () => {
            myChart.resize();
        };
        window.addEventListener('resize', resizeHandler);

        // 10. 监听主题变化
        this.addThemeChangeListener(container, mindMapData, videoTitle);

        // 11. 添加鼠标滚轮缩放提示
        let zoomTipShown = false;
        container.addEventListener('wheel', (e) => {
            if (!zoomTipShown) {
                // 显示缩放提示
                const tip = document.createElement('div');
                tip.style.cssText = `
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: ${colorScheme.tooltipBg};
                    color: ${colorScheme.tooltipText};
                    padding: 10px 15px;
                    border-radius: 5px;
                    font-size: 14px;
                    z-index: 1000;
                    pointer-events: none;
                    border: 1px solid ${colorScheme.tooltipBorder};
                `;
                tip.textContent = '可以滚轮缩放、拖拽移动、点击节点展开/折叠';
                container.appendChild(tip);
                
                setTimeout(() => {
                    if (tip.parentNode) {
                        tip.parentNode.removeChild(tip);
                    }
                }, 2000);
                
                zoomTipShown = true;
            }
        });

        // 12. 清理函数，当模态框关闭时调用
        this.mindMapCleanup = () => {
            window.removeEventListener('resize', resizeHandler);
            myChart.dispose();
        };
    }

    // 检测当前是否为黑暗模式
    isDarkMode() {
        // 检查HTML元素是否有dark-mode类
        if (document.documentElement.classList.contains('dark-mode')) {
            return true;
        }
        
        // 检查body元素是否有dark-mode类
        if (document.body.classList.contains('dark-mode')) {
            return true;
        }
        
        // 检查localStorage中的设置
        if (localStorage.getItem('darkMode') === 'true') {
            return true;
        }
        
        // 检查Chrome扩展的存储（如果在扩展环境中）
        if (typeof chrome !== 'undefined' && chrome.storage) {
            chrome.storage.sync.get(['darkMode'], (result) => {
                return result.darkMode === true;
            });
        }
        
        // 检查系统主题偏好
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return true;
        }
        
        return false;
    }

    // 获取颜色方案
    getColorScheme(isDarkMode) {
        if (isDarkMode) {
            // 黑暗模式颜色方案
            return {
                backgroundColor: '#1a202c',
                tooltipBg: '#2d3748',
                tooltipBorder: '#4a5568',
                tooltipText: '#e2e8f0',
                labelBg: '#2d3748',
                labelBorder: '#4a5568',
                rootBg: '#4299e1',
                rootText: '#ffffff',
                branchText: '#ffffff',
                leafBg: '#374151',
                leafBorder: '#4b5563',
                leafText: '#e5e7eb',
                lineColor: '#4a5568',
                branchColors: [
                    { name: 'green', color: '#48bb78' },
                    { name: 'orange', color: '#ed8936' },
                    { name: 'red', color: '#f56565' },
                    { name: 'purple', color: '#9f7aea' },
                    { name: 'cyan', color: '#4fd1c7' },
                    { name: 'lime', color: '#68d391' },
                    { name: 'blue', color: '#4299e1' },
                    { name: 'magenta', color: '#d53f8c' },
                    { name: 'yellow', color: '#ecc94b' },
                    { name: 'violet', color: '#805ad5' },
                    { name: 'coral', color: '#fc8181' },
                    { name: 'turquoise', color: '#38b2ac' },
                    { name: 'skyblue', color: '#63b3ed' },
                    { name: 'mint', color: '#81c784' },
                    { name: 'cream', color: '#f6e05e' },
                    { name: 'plum', color: '#b794f6' }
                ]
            };
        } else {
            // 明亮模式颜色方案
            return {
                backgroundColor: '#f9f9f9',
                tooltipBg: 'rgba(0, 0, 0, 0.8)',
                tooltipBorder: '#ccc',
                tooltipText: '#fff',
                labelBg: '#fff',
                labelBorder: '#ccc',
                rootBg: '#4A90E2',
                rootText: '#fff',
                branchText: '#fff',
                leafBg: '#f7f7f7',
                leafBorder: '#ddd',
                leafText: '#333',
                lineColor: '#ccc',
                branchColors: [
                    { name: 'green', color: '#7ED321' },
                    { name: 'orange', color: '#F5A623' },
                    { name: 'red', color: '#D0021B' },
                    { name: 'purple', color: '#9013FE' },
                    { name: 'cyan', color: '#50E3C2' },
                    { name: 'lime', color: '#B8E986' },
                    { name: 'blue', color: '#4A90E2' },
                    { name: 'magenta', color: '#BD10E0' },
                    { name: 'yellow', color: '#F8E71C' },
                    { name: 'violet', color: '#7B68EE' },
                    { name: 'coral', color: '#FF6B6B' },
                    { name: 'turquoise', color: '#4ECDC4' },
                    { name: 'skyblue', color: '#45B7D1' },
                    { name: 'mint', color: '#96CEB4' },
                    { name: 'cream', color: '#FFEAA7' },
                    { name: 'plum', color: '#DDA0DD' }
                ]
            };
        }
    }

    // 添加自定义缩放控制按钮
    addZoomControls(container, myChart, colorScheme) {
        // 创建控制按钮容器
        const controlsContainer = document.createElement('div');
        controlsContainer.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
            z-index: 1000;
        `;

        // 按钮样式
        const buttonStyle = `
            width: 32px;
            height: 32px;
            border: none;
            background: ${colorScheme.labelBg};
            color: ${colorScheme.leafText};
            border: 1px solid ${colorScheme.labelBorder};
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        `;

        // 放大按钮
        const zoomInBtn = document.createElement('button');
        zoomInBtn.innerHTML = '+';
        zoomInBtn.style.cssText = buttonStyle + 'font-size: 18px;';
        zoomInBtn.addEventListener('click', () => {
            // 获取当前缩放级别并放大
            const option = myChart.getOption();
            const currentZoom = option.series[0].zoom || 1;
            const newZoom = Math.min(currentZoom * 1.2, 3); // 最大3倍
            
            myChart.setOption({
                series: [{
                    zoom: newZoom
                }]
            });
        });

        // 缩小按钮
        const zoomOutBtn = document.createElement('button');
        zoomOutBtn.innerHTML = '-';
        zoomOutBtn.style.cssText = buttonStyle + 'font-size: 18px;';
        zoomOutBtn.addEventListener('click', () => {
            // 获取当前缩放级别并缩小
            const option = myChart.getOption();
            const currentZoom = option.series[0].zoom || 1;
            const newZoom = Math.max(currentZoom * 0.8, 0.5); // 最小0.5倍
            
            myChart.setOption({
                series: [{
                    zoom: newZoom
                }]
            });
        });

        // 还原按钮
        const resetBtn = document.createElement('button');
        resetBtn.innerHTML = '⌂';
        resetBtn.style.cssText = buttonStyle;
        resetBtn.addEventListener('click', () => {
            // 重置缩放和位置
            myChart.setOption({
                series: [{
                    zoom: 1,
                    center: null // 重置中心点
                }]
            });
        });

        // 添加悬停效果
        [zoomInBtn, zoomOutBtn, resetBtn].forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.backgroundColor = colorScheme.leafBg;
                btn.style.transform = 'translateY(-1px)';
            });
            btn.addEventListener('mouseleave', () => {
                btn.style.backgroundColor = colorScheme.labelBg;
                btn.style.transform = 'translateY(0)';
            });
        });

        // 添加按钮到容器
        controlsContainer.appendChild(zoomInBtn);
        controlsContainer.appendChild(zoomOutBtn);
        controlsContainer.appendChild(resetBtn);

        // 添加到图表容器
        container.appendChild(controlsContainer);
    }

    // 添加主题变化监听器
    addThemeChangeListener(container, mindMapData, videoTitle) {
        // 监听localStorage变化
        window.addEventListener('storage', (e) => {
            if (e.key === 'darkMode') {
                this.refreshMindMap(container, mindMapData, videoTitle);
            }
        });

        // 监听系统主题变化
        if (window.matchMedia) {
            const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            darkModeMediaQuery.addListener(() => {
                this.refreshMindMap(container, mindMapData, videoTitle);
            });
        }

        // 监听DOM类变化（用于检测手动切换）
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && 
                    (mutation.attributeName === 'class') &&
                    (mutation.target === document.documentElement || mutation.target === document.body)) {
                    this.refreshMindMap(container, mindMapData, videoTitle);
                }
            });
        });

        observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });
        observer.observe(document.body, { attributes: true, attributeFilter: ['class'] });
    }

    // 刷新思维导图（主题变化时）
    refreshMindMap(container, mindMapData, videoTitle) {
        // 清空容器
        container.innerHTML = '';
        // 重新渲染
        this.renderMindMapWithECharts(container, mindMapData, videoTitle);
    }
    
    // 显示摘要错误信息
    showSummaryError(loadingEl, title, message) {
        if (loadingEl) {
            const loadingText = loadingEl.querySelector('.loading-text');
            const loadingSubtext = loadingEl.querySelector('.loading-subtext');
            if (loadingText) loadingText.textContent = title;
            if (loadingSubtext) loadingSubtext.textContent = message;
        }
        
        // 2秒后关闭模态框
        setTimeout(() => {
            const summaryModal = document.getElementById('extension-summary-modal');
            if (summaryModal) {
                summaryModal.style.display = 'none';
            }
        }, 3000);
    }

    // 跳转到上一句字幕
    jumpToPreviousSubtitle() {
        const videoElement = this.core.playerContainer.querySelector('video');
        if (!videoElement) return;
        
        const subtitles = this.core.jaSubtitles || [];
        if (subtitles.length === 0) {
            // 如果没有字幕数据，回退10秒
            videoElement.currentTime = Math.max(0, videoElement.currentTime - 10);
            return;
        }
        
        const currentIndex = SubtitleUtils.findCurrentSubtitleIndex(subtitles, videoElement.currentTime);
        let targetIndex = currentIndex - 1;
        
        // 如果当前已经是第一个字幕或者没找到当前字幕，跳到开头
        if (targetIndex < 0) {
            targetIndex = 0;
        }
        
        // 跳转到目标字幕的开始时间
        if (subtitles[targetIndex]) {
            videoElement.currentTime = subtitles[targetIndex].start;
            console.log(`跳转到上一句字幕: ${subtitles[targetIndex].text} (${SubtitleUtils.formatTime(subtitles[targetIndex].start)})`);
        }
    }

    // 跳转到下一句字幕
    jumpToNextSubtitle() {
        const videoElement = this.core.playerContainer.querySelector('video');
        if (!videoElement) return;
        
        const subtitles = this.core.jaSubtitles || [];
        if (subtitles.length === 0) {
            // 如果没有字幕数据，前进10秒
            videoElement.currentTime = Math.min(videoElement.duration, videoElement.currentTime + 10);
            return;
        }
        
        const currentIndex = SubtitleUtils.findCurrentSubtitleIndex(subtitles, videoElement.currentTime);
        let targetIndex = currentIndex + 1;
        
        // 如果已经是最后一个字幕，不移动
        if (targetIndex >= subtitles.length) {
            targetIndex = subtitles.length - 1;
        }
        
        // 跳转到目标字幕的开始时间
        if (subtitles[targetIndex]) {
            videoElement.currentTime = subtitles[targetIndex].start;
            console.log(`跳转到下一句字幕: ${subtitles[targetIndex].text} (${SubtitleUtils.formatTime(subtitles[targetIndex].start)})`);
        }
    }

    // 清理分析管理器
    cleanup() {
        console.log('开始清理字幕分析管理器');

        // 清理分析面板相关元素
        const analysisModal = document.getElementById('extension-analysis-modal');
        if (analysisModal) {
            analysisModal.remove();
        }

        console.log('字幕分析管理器清理完成');
    }
}

// 注册到全局对象
window.SubtitleAnalysis = SubtitleAnalysis; 