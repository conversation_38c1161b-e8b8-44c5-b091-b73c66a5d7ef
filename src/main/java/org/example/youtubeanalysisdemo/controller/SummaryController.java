package org.example.youtubeanalysisdemo.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.example.youtubeanalysisdemo.service.SubscriptionService;
import org.example.youtubeanalysisdemo.service.MistralSummaryService;
import org.example.youtubeanalysisdemo.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@RestController
@RequestMapping("/api/summary")
public class SummaryController {

    private static final Logger logger = LoggerFactory.getLogger(SummaryController.class);
    
    @Value("${subtitles.directory:src/main/resources/static/subtitles}")
    private String subtitlesDirectory;

    @Autowired
    private SubscriptionService subscriptionService;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private MistralSummaryService mistralSummaryService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @PostMapping("/generate")
    public ResponseEntity<Map<String, Object>> generateSummary(
            @RequestBody SummaryRequest request,
            @AuthenticationPrincipal OAuth2User principal) {
        logger.info("收到视频摘要请求: subtitleFileId={}, videoUrl={}, hasSubtitleContent={}",
                request.getSubtitleFileId(), request.getVideoUrl(),
                request.getSubtitleContent() != null && !request.getSubtitleContent().trim().isEmpty());

        try {
            // 1. 验证用户认证状态
            if (principal == null) {
                logger.warn("用户未认证，无法使用视频摘要功能");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of(
                    "success", false,
                    "error", "AUTHENTICATION_REQUIRED",
                    "message", "请先登录后再使用视频摘要功能"
                ));
            }
            
            // 2. 获取用户ID并验证Premium会员权限
            String googleId = principal.getAttribute("sub");
            if (googleId == null) {
                googleId = principal.getAttribute("id");
            }
            
            if (googleId == null) {
                logger.error("无法获取用户ID，认证信息异常");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of(
                    "success", false,
                    "error", "INVALID_USER_INFO",
                    "message", "用户认证信息异常，请重新登录"
                ));
            }
            
            // 3. 检查Premium会员权限
            boolean isPremiumMember = subscriptionService.isPremiumMember(googleId);
            if (!isPremiumMember) {
                logger.warn("用户 {} 不是Premium会员，无法使用视频摘要功能", googleId);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(Map.of(
                    "success", false,
                    "error", "PREMIUM_REQUIRED",
                    "message", "视频摘要功能仅限Premium会员使用，请升级您的会员等级"
                ));
            }
            
            logger.info("用户 {} 是Premium会员，允许使用视频摘要功能", googleId);

            // 4. 验证请求参数
            if ((request.getSubtitleFileId() == null || request.getSubtitleFileId().trim().isEmpty())
                && (request.getSubtitleContent() == null || request.getSubtitleContent().trim().isEmpty())) {
                logger.error("字幕文件ID和字幕内容都为空");
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "字幕文件ID或字幕内容不能同时为空"
                ));
            }

            // 5. 获取字幕内容（优先使用前端传递的内容）
            String subtitleContent;
            String videoId;

            if (request.getSubtitleContent() != null && !request.getSubtitleContent().trim().isEmpty()) {
                // 使用前端传递的字幕内容
                logger.info("使用前端传递的字幕内容，大小: {} 字节", request.getSubtitleContent().length());
                subtitleContent = request.getSubtitleContent();

                // 从URL或subtitleFileId提取videoId
                if (request.getSubtitleFileId() != null && request.getSubtitleFileId().contains("_")) {
                    videoId = request.getSubtitleFileId().split("_")[0];
                } else {
                    videoId = extractVideoId(request.getVideoUrl());
                }

                if (videoId == null || videoId.isEmpty()) {
                    videoId = "unknown";
                }

            } else {
                // 回退到文件读取方式
                logger.info("前端未提供字幕内容，回退到文件读取方式");

                // 获取字幕文件路径
                Path subtitlesDirPath = Paths.get(subtitlesDirectory);

                // 从subtitleFileId中提取videoId (格式通常是: videoId_uuid)
                final String extractedVideoId;
                if (request.getSubtitleFileId().contains("_")) {
                    extractedVideoId = request.getSubtitleFileId().split("_")[0];
                } else {
                    // 如果无法从subtitleFileId提取videoId，尝试从URL提取
                    extractedVideoId = extractVideoId(request.getVideoUrl());
                }

                if (extractedVideoId == null || extractedVideoId.isEmpty()) {
                    logger.error("无法确定视频ID");
                    return ResponseEntity.badRequest().body(Map.of(
                        "success", false,
                        "message", "无法确定视频ID"
                    ));
                }

                videoId = extractedVideoId;
                logger.info("查找视频ID为{}的字幕文件", videoId);

                // 查找与subtitleFileId匹配的字幕文件
                File[] matchingFiles = subtitlesDirPath.toFile().listFiles((dir, name) ->
                    name.contains(request.getSubtitleFileId()) && name.toLowerCase().endsWith(".vtt"));

                // 如果没有找到精确匹配，尝试查找包含videoId的文件
                if (matchingFiles == null || matchingFiles.length == 0) {
                    logger.warn("未找到匹配subtitleFileId的文件，尝试查找包含videoId的文件: {}", videoId);
                    matchingFiles = subtitlesDirPath.toFile().listFiles((dir, name) ->
                        name.startsWith(videoId + "_") && name.toLowerCase().endsWith(".vtt"));
                }

                if (matchingFiles == null || matchingFiles.length == 0) {
                    logger.error("未找到字幕文件: subtitleFileId={}, videoId={}", request.getSubtitleFileId(), videoId);
                    return ResponseEntity.badRequest().body(Map.of(
                        "success", false,
                        "message", "未找到对应的字幕文件"
                    ));
                }

                // 选择文件（优先选择中文字幕，如果没有则选择第一个）
                File selectedFile = null;
                for (File file : matchingFiles) {
                    String fileName = file.getName().toLowerCase();
                    if (fileName.contains(".zh-hans") || fileName.contains(".zh-hant") || fileName.contains(".zh.")) {
                        selectedFile = file;
                        logger.info("选择中文字幕文件: {}", file.getName());
                        break;
                    }
                }
                if (selectedFile == null) {
                    selectedFile = matchingFiles[0];
                    logger.info("未找到中文字幕，选择默认文件: {}", selectedFile.getName());
                }

                // 读取字幕文件
                subtitleContent = readSubtitleFile(selectedFile);
                logger.info("成功读取字幕文件: {}, 大小: {} 字节", selectedFile.getName(), subtitleContent.length());
            }

            // 提取视频标题
            String videoTitle = "YouTube视频: " + videoId;

            // 处理字幕内容
            String processedSubtitles;
            if (subtitleContent.trim().startsWith("{") || subtitleContent.trim().startsWith("[")) {
                // JSON格式的字幕数据（来自前端）
                logger.info("处理JSON格式的字幕数据");
                processedSubtitles = processJsonSubtitleData(subtitleContent);
            } else {
                // VTT格式的字幕数据（来自文件）
                logger.info("处理VTT格式的字幕数据");
                processedSubtitles = processSubtitles(subtitleContent);
            }

            // 检查处理后的字幕内容是否为空
            if (processedSubtitles == null || processedSubtitles.trim().isEmpty()) {
                logger.error("处理后的字幕内容为空");
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "字幕内容为空，无法生成摘要"
                ));
            }

            logger.info("处理后的字幕内容长度: {} 字符", processedSubtitles.length());

            // 生成摘要
            String summary = mistralSummaryService.generateSummaryFromSubtitles(videoTitle, processedSubtitles);
            
            logger.info("成功生成视频摘要");
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "summary", summary,
                "videoTitle", videoTitle
            ));
            
        } catch (Exception e) {
            logger.error("生成视频摘要失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "message", "生成摘要失败: " + e.getMessage()
            ));
        }
    }

    // 读取字幕文件内容
    private String readSubtitleFile(File file) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }
    
    // 从URL中提取视频ID
    private String extractVideoId(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }
        
        Pattern pattern = Pattern.compile("(?:youtube\\.com/.*[?&]v=|youtu\\.be/)([^\"&?/\\s]{11})");
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        return null;
    }
    
    // 处理字幕内容，提取纯文本
    private String processSubtitles(String vttContent) {
        if (vttContent == null || vttContent.isEmpty()) {
            return "";
        }

        // 创建用于存储文本的列表
        List<String> textLines = new ArrayList<>();

        // 按行分割字幕内容
        String[] lines = vttContent.split("\n");

        // 跳过WEBVTT头
        boolean headerPassed = false;

        for (String line : lines) {
            // 跳过头部
            if (!headerPassed) {
                if (line.trim().isEmpty()) {
                    headerPassed = true;
                }
                continue;
            }

            // 跳过时间戳行和空行
            if (line.trim().isEmpty() || line.contains("-->") || line.matches("\\d+")) {
                continue;
            }

            // 提取纯文本（去除HTML标签等）
            String cleanedLine = line.replaceAll("<[^>]*>", "").trim();
            if (!cleanedLine.isEmpty()) {
                textLines.add(cleanedLine);
            }
        }

        // 连接所有文本行，使用空格分隔
        return String.join(" ", textLines);
    }

    // 处理前端传来的JSON格式字幕数据
    private String processJsonSubtitleData(String jsonContent) {
        if (jsonContent == null || jsonContent.isEmpty()) {
            return "";
        }

        try {
            JsonNode rootNode = objectMapper.readTree(jsonContent);
            List<String> textLines = new ArrayList<>();

            // 检查是否有events数组
            JsonNode eventsNode = rootNode.get("events");
            if (eventsNode != null && eventsNode.isArray()) {
                for (JsonNode eventNode : eventsNode) {
                    // 获取segs数组
                    JsonNode segsNode = eventNode.get("segs");
                    if (segsNode != null && segsNode.isArray()) {
                        for (JsonNode segNode : segsNode) {
                            // 获取utf8文本
                            JsonNode utf8Node = segNode.get("utf8");
                            if (utf8Node != null && !utf8Node.isNull()) {
                                String text = utf8Node.asText().trim();
                                if (!text.isEmpty()) {
                                    textLines.add(text);
                                }
                            }
                        }
                    }
                }
            }

            // 连接所有文本行，使用空格分隔
            return String.join(" ", textLines);

        } catch (Exception e) {
            logger.error("解析JSON字幕数据失败", e);
            return "";
        }
    }
    

    
    // 请求体类
    static class SummaryRequest {
        private String subtitleFileId;
        private String videoUrl;
        private String subtitleContent; // 新增：前端传递的字幕内容

        public String getSubtitleFileId() {
            return subtitleFileId;
        }

        public void setSubtitleFileId(String subtitleFileId) {
            this.subtitleFileId = subtitleFileId;
        }

        public String getVideoUrl() {
            return videoUrl;
        }

        public void setVideoUrl(String videoUrl) {
            this.videoUrl = videoUrl;
        }

        public String getSubtitleContent() {
            return subtitleContent;
        }

        public void setSubtitleContent(String subtitleContent) {
            this.subtitleContent = subtitleContent;
        }
    }
} 